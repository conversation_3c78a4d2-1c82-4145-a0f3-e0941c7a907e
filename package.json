{"name": "the-library", "title": "The Library", "version": "1.0.2", "homepage": "https://github.com/Abdalsalaam/the-library", "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}, "scripts": {"build": "pnpm makepot && pnpm archive", "archive": "composer archive --file=$npm_package_name --format=zip", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude vendor,node_modules,tests,docs"}, "devDependencies": {"@wordpress/scripts": "^27.4.0", "node-wp-i18n": "~1.2.3"}}