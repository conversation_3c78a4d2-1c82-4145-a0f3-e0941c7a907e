# Copyright (C) 2025 <PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the GPL v3 or later.
msgid ""
msgstr ""
"Project-Id-Version: The Library 1.0.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/the-library\n"
"POT-Creation-Date: 2025-08-19 15:45:34+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2025-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"X-Generator: node-wp-i18n 1.2.8\n"

#: includes/class-admin.php:37 includes/class-admin.php:38
#: includes/class-admin.php:119 includes/class-admin.php:480
msgid "Download Requests"
msgstr ""

#: includes/class-admin.php:46 includes/class-admin.php:47
#: includes/class-admin.php:342
msgid "System Logs"
msgstr ""

#: includes/class-admin.php:55 includes/class-admin.php:446
msgid "System Maintenance"
msgstr ""

#: includes/class-admin.php:56
msgid "Maintenance"
msgstr ""

#: includes/class-admin.php:71
msgid "Security check failed. Please try again."
msgstr ""

#: includes/class-admin.php:76 includes/class-admin.php:268
msgid "You do not have sufficient permissions to perform this action."
msgstr ""

#: includes/class-admin.php:87
#. translators: %d: number of deleted requests
msgid "%d request deleted successfully."
msgid_plural "%d requests deleted successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/class-admin.php:92
msgid "Error deleting requests. Please try again."
msgstr ""

#: includes/class-admin.php:136
msgid "Export CSV"
msgstr ""

#: includes/class-admin.php:146
msgid "Search requests:"
msgstr ""

#: includes/class-admin.php:147
msgid "Search by name, email, or mobile..."
msgstr ""

#: includes/class-admin.php:148
msgid "Search"
msgstr ""

#: includes/class-admin.php:158
msgid "Bulk Actions"
msgstr ""

#: includes/class-admin.php:159 includes/class-admin.php:248
msgid "Delete"
msgstr ""

#: includes/class-admin.php:161
msgid "Apply"
msgstr ""

#: includes/class-admin.php:169
#. translators: %s: number of items
msgid "%s item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/class-admin.php:199
msgid "File"
msgstr ""

#: includes/class-admin.php:200
msgid "Name"
msgstr ""

#: includes/class-admin.php:201
msgid "Email"
msgstr ""

#: includes/class-admin.php:202
msgid "Mobile"
msgstr ""

#: includes/class-admin.php:203 includes/class-csv-export.php:80
msgid "Download Date"
msgstr ""

#: includes/class-admin.php:204 includes/class-admin.php:373
#: includes/class-csv-export.php:81
msgid "IP Address"
msgstr ""

#: includes/class-admin.php:205
msgid "Actions"
msgstr ""

#: includes/class-admin.php:211
msgid "No download requests found."
msgstr ""

#: includes/class-admin.php:230 includes/class-csv-export.php:92
msgid "File not found"
msgstr ""

#: includes/class-admin.php:240 includes/class-download-handler.php:329
msgid "Not provided"
msgstr ""

#: includes/class-admin.php:273 includes/class-admin.php:279
msgid "Invalid request ID."
msgstr ""

#: includes/class-admin.php:285
msgid "Request deleted successfully."
msgstr ""

#: includes/class-admin.php:287
msgid "Error deleting request or request not found."
msgstr ""

#: includes/class-admin.php:321
msgid "Are you sure you want to delete this request?"
msgstr ""

#: includes/class-admin.php:322
msgid "Error deleting request."
msgstr ""

#: includes/class-admin.php:323
msgid "Are you sure you want to clear old logs?"
msgstr ""

#: includes/class-admin.php:324
msgid "Failed to clear logs."
msgstr ""

#: includes/class-admin.php:325
msgid "Running..."
msgstr ""

#: includes/class-admin.php:326
msgid "Cleanup failed. Please try again."
msgstr ""

#: includes/class-admin.php:327
msgid "Clearing..."
msgstr ""

#: includes/class-admin.php:328
msgid "Failed to clear cache. Please try again."
msgstr ""

#: includes/class-admin.php:347
msgid "Error logs table does not exist."
msgstr ""

#: includes/class-admin.php:348
msgid ""
"Please deactivate and reactivate the plugin to create the required database "
"tables."
msgstr ""

#: includes/class-admin.php:356
msgid "Clear Old Logs"
msgstr ""

#: includes/class-admin.php:362
msgid "Error logs table is not available. Please reactivate the plugin."
msgstr ""

#: includes/class-admin.php:364
msgid "No error logs found."
msgstr ""

#: includes/class-admin.php:369
msgid "Timestamp"
msgstr ""

#: includes/class-admin.php:370
msgid "Level"
msgstr ""

#: includes/class-admin.php:371
msgid "Message"
msgstr ""

#: includes/class-admin.php:372
msgid "User"
msgstr ""

#: includes/class-admin.php:399
msgid "Guest"
msgstr ""

#: includes/class-admin.php:419 includes/class-admin.php:556
#: includes/class-admin.php:609
msgid "Insufficient permissions."
msgstr ""

#: includes/class-admin.php:428
#. translators: %d: number of deleted logs
msgid "%d old logs cleared successfully."
msgstr ""

#: includes/class-admin.php:449
msgid "Database Statistics"
msgstr ""

#: includes/class-admin.php:452
msgid "Total Downloads"
msgstr ""

#: includes/class-admin.php:456
msgid "Today"
msgstr ""

#: includes/class-admin.php:460
msgid "This Week"
msgstr ""

#: includes/class-admin.php:464
msgid "This Month"
msgstr ""

#: includes/class-admin.php:468 includes/class-admin.php:489
msgid "Error Logs"
msgstr ""

#: includes/class-admin.php:475
msgid "Database Cleanup"
msgstr ""

#: includes/class-admin.php:476
msgid "Clean up old data to optimize database performance."
msgstr ""

#: includes/class-admin.php:483
msgid "Clean Old Download Requests"
msgstr ""

#: includes/class-admin.php:485
msgid "Remove download requests older than 1 year."
msgstr ""

#: includes/class-admin.php:492
msgid "Clean Old Error Logs"
msgstr ""

#: includes/class-admin.php:494
msgid "Remove error logs older than 30 days."
msgstr ""

#: includes/class-admin.php:498
msgid "Expired Transients"
msgstr ""

#: includes/class-admin.php:501
msgid "Clean Expired Transients"
msgstr ""

#: includes/class-admin.php:503
msgid "Remove expired download tokens and cache data."
msgstr ""

#: includes/class-admin.php:507
msgid "Full Cleanup"
msgstr ""

#: includes/class-admin.php:510
msgid "Run Full Cleanup"
msgstr ""

#: includes/class-admin.php:512
msgid "Run all cleanup tasks at once."
msgstr ""

#: includes/class-admin.php:521
msgid "Cache Management"
msgstr ""

#: includes/class-admin.php:522
msgid "Manage plugin cache for better performance."
msgstr ""

#: includes/class-admin.php:526
msgid "File Types Cache"
msgstr ""

#: includes/class-admin.php:529
msgid "Clear File Types Cache"
msgstr ""

#: includes/class-admin.php:531
msgid "Force refresh of file types dropdown cache."
msgstr ""

#: includes/class-admin.php:535
msgid "All Caches"
msgstr ""

#: includes/class-admin.php:538
msgid "Clear All Plugin Caches"
msgstr ""

#: includes/class-admin.php:540
msgid "Clear all plugin-related cache data."
msgstr ""

#: includes/class-admin.php:561 includes/class-admin.php:595
msgid "Invalid cleanup type."
msgstr ""

#: includes/class-admin.php:573
#. translators: %d: number of deleted requests
msgid "Cleaned up %d old download requests."
msgstr ""

#: includes/class-admin.php:579
#. translators: %d: number of deleted logs
msgid "Cleaned up %d error logs."
msgstr ""

#: includes/class-admin.php:586
#. translators: 1: number of download requests cleaned, 2: number of error logs
#. cleaned, 3: number of transients cleaned
msgid ""
"Full cleanup completed. Download requests: %1$d, Error logs: %2$d, "
"Transients: %3$d"
msgstr ""

#: includes/class-admin.php:614 includes/class-admin.php:632
msgid "Invalid cache type."
msgstr ""

#: includes/class-admin.php:623
msgid "File types cache cleared successfully."
msgstr ""

#: includes/class-admin.php:628
msgid "All plugin caches cleared successfully."
msgstr ""

#: includes/class-csv-export.php:40
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: includes/class-csv-export.php:55
msgid "No download requests found to export."
msgstr ""

#: includes/class-csv-export.php:75
msgid "ID"
msgstr ""

#: includes/class-csv-export.php:76
msgid "File Title"
msgstr ""

#: includes/class-csv-export.php:77
msgid "User Name"
msgstr ""

#: includes/class-csv-export.php:78
msgid "User Email"
msgstr ""

#: includes/class-csv-export.php:79
msgid "User Mobile"
msgstr ""

#: includes/class-csv-export.php:82
msgid "User Agent"
msgstr ""

#: includes/class-custom-post-type.php:42
msgid "Add New"
msgstr ""

#: includes/class-custom-post-type.php:43
msgid "Add New File"
msgstr ""

#: includes/class-custom-post-type.php:44
msgid "New File"
msgstr ""

#: includes/class-custom-post-type.php:45
msgid "Edit File"
msgstr ""

#: includes/class-custom-post-type.php:46
msgid "View File"
msgstr ""

#: includes/class-custom-post-type.php:47
msgid "All Files"
msgstr ""

#: includes/class-custom-post-type.php:48
msgid "Search Files"
msgstr ""

#: includes/class-custom-post-type.php:49
msgid "Parent Files:"
msgstr ""

#: includes/class-custom-post-type.php:50
msgid "No files found."
msgstr ""

#: includes/class-custom-post-type.php:51
msgid "No files found in Trash."
msgstr ""

#: includes/class-custom-post-type.php:92
msgid "Search File Categories"
msgstr ""

#: includes/class-custom-post-type.php:93
msgid "All File Categories"
msgstr ""

#: includes/class-custom-post-type.php:94
msgid "Parent File Category"
msgstr ""

#: includes/class-custom-post-type.php:95
msgid "Parent File Category:"
msgstr ""

#: includes/class-custom-post-type.php:96
msgid "Edit File Category"
msgstr ""

#: includes/class-custom-post-type.php:97
msgid "Update File Category"
msgstr ""

#: includes/class-custom-post-type.php:98
msgid "Add New File Category"
msgstr ""

#: includes/class-custom-post-type.php:99
msgid "New File Category Name"
msgstr ""

#: includes/class-custom-post-type.php:100
msgid "File Categories"
msgstr ""

#: includes/class-custom-post-type.php:122
msgid "File Upload"
msgstr ""

#: includes/class-custom-post-type.php:131
msgid "File Details"
msgstr ""

#: includes/class-custom-post-type.php:155
#: includes/class-custom-post-type.php:160
msgid "Upload File"
msgstr ""

#: includes/class-custom-post-type.php:161
msgid "Remove File"
msgstr ""

#: includes/class-custom-post-type.php:162
msgid "Upload the file that users will be able to download."
msgstr ""

#: includes/class-custom-post-type.php:178
#: templates/single-files-library.php:147
msgid "File Size:"
msgstr ""

#: includes/class-custom-post-type.php:178
#: includes/class-custom-post-type.php:179
msgid "N/A"
msgstr ""

#: includes/class-custom-post-type.php:179
#: templates/single-files-library.php:154
msgid "File Type:"
msgstr ""

#: includes/class-custom-post-type.php:180
msgid "Download Count:"
msgstr ""

#: includes/class-download-handler.php:41
msgid "Required fields are missing."
msgstr ""

#: includes/class-download-handler.php:55
msgid "Please fill in all required fields."
msgstr ""

#: includes/class-download-handler.php:64
msgid "Please enter a valid mobile number (minimum 7 digits)."
msgstr ""

#: includes/class-download-handler.php:73
msgid "Please enter a valid email address."
msgstr ""

#: includes/class-download-handler.php:83
#: includes/class-download-handler.php:176
#: includes/class-download-handler.php:229
#: includes/class-download-handler.php:315
msgid "File not found."
msgstr ""

#: includes/class-download-handler.php:113
msgid "Database error occurred. Please try again."
msgstr ""

#: includes/class-download-handler.php:121
msgid "Thank you! Your download will start shortly."
msgstr ""

#: includes/class-download-handler.php:137
msgid "Download token is required."
msgstr ""

#: includes/class-download-handler.php:147
msgid "Invalid download token."
msgstr ""

#: includes/class-download-handler.php:165
msgid "Download token has expired. Please request the file again."
msgstr ""

#: includes/class-download-handler.php:204
msgid "Invalid download request. Missing parameters."
msgstr ""

#: includes/class-download-handler.php:211
msgid "Invalid download request."
msgstr ""

#: includes/class-download-handler.php:218
msgid "Download token has expired or is invalid. Please request the file again."
msgstr ""

#: includes/class-download-handler.php:223
msgid "Invalid download token for this file."
msgstr ""

#: includes/class-download-handler.php:241
msgid "File not found on server."
msgstr ""

#: includes/class-download-handler.php:284
msgid "You must be logged in to download files directly."
msgstr ""

#: includes/class-download-handler.php:295
msgid "Post ID is required."
msgstr ""

#: includes/class-download-handler.php:305
msgid "Invalid post ID."
msgstr ""

#: includes/class-download-handler.php:350
msgid "Error saving your request. Please try again."
msgstr ""

#: includes/class-frontend.php:73
msgid "Loading..."
msgstr ""

#: includes/class-frontend.php:74
msgid "Error loading files. Please try again."
msgstr ""

#: includes/class-frontend.php:221 includes/class-frontend.php:251
#. translators: %s: file title
msgid "Download %s"
msgstr ""

#: includes/class-frontend.php:232
#. translators: %s: file title
msgid "View details for %s"
msgstr ""

#: includes/class-frontend.php:272
#. translators: %s: category name.
msgid "View all %s files"
msgstr ""

#: includes/class-frontend.php:297
msgid "File size:"
msgstr ""

#: includes/class-frontend.php:305
msgid "File type:"
msgstr ""

#: includes/class-frontend.php:312
msgid "Download count:"
msgstr ""

#: includes/class-frontend.php:316
#. translators: %d: download count
msgid "download"
msgid_plural "downloads"
msgstr[0] ""
msgstr[1] ""

#: includes/class-frontend.php:323 templates/single-files-library.php:165
msgid "Published:"
msgstr ""

#: includes/class-frontend.php:339
#. translators: %s: file title
msgid "View details and download %s"
msgstr ""

#: includes/class-frontend.php:345
msgid "View Details"
msgstr ""

#: includes/class-frontend.php:351
#. translators: %s: file title
msgid "for %s"
msgstr ""

#: templates/archive-files-library.php:22
msgid "Files in Category: "
msgstr ""

#: templates/archive-files-library.php:24
msgid "Files Library"
msgstr ""

#: templates/archive-files-library.php:60
msgid "Search files..."
msgstr ""

#: templates/archive-files-library.php:62
msgid "Search files"
msgstr ""

#: templates/archive-files-library.php:64
msgid "Clear search"
msgstr ""

#: templates/archive-files-library.php:76
msgid "Category"
msgstr ""

#: templates/archive-files-library.php:79
msgid "Filter by category"
msgstr ""

#: templates/archive-files-library.php:80
msgid "All Categories"
msgstr ""

#: templates/archive-files-library.php:105
msgid "File Type"
msgstr ""

#: templates/archive-files-library.php:108
msgid "Filter by file type"
msgstr ""

#: templates/archive-files-library.php:109
msgid "All File Types"
msgstr ""

#: templates/archive-files-library.php:130
msgid "Sort By"
msgstr ""

#: templates/archive-files-library.php:133
msgid "Sort files"
msgstr ""

#: templates/archive-files-library.php:136
msgid "Newest First"
msgstr ""

#: templates/archive-files-library.php:137
msgid "Oldest First"
msgstr ""

#: templates/archive-files-library.php:138
msgid "Title A-Z"
msgstr ""

#: templates/archive-files-library.php:139
msgid "Title Z-A"
msgstr ""

#: templates/archive-files-library.php:140
msgid "Most Downloaded"
msgstr ""

#: templates/archive-files-library.php:163
msgid "Apply Filters"
msgstr ""

#: templates/archive-files-library.php:176
msgid "Clear All"
msgstr ""

#: templates/archive-files-library.php:202
#. translators: 1: start number, 2: end number, 3: total number
msgid "Showing %1$d-%2$d of %3$d files"
msgstr ""

#: templates/archive-files-library.php:242
msgid "&laquo; Previous"
msgstr ""

#: templates/archive-files-library.php:243
msgid "Next &raquo;"
msgstr ""

#: templates/archive-files-library.php:255
msgid "No files found"
msgstr ""

#: templates/archive-files-library.php:256
msgid "Try adjusting your search criteria or browse all files."
msgstr ""

#: templates/archive-files-library.php:258
msgid "Browse All Files"
msgstr ""

#: templates/single-files-library.php:53
msgid "Download This File"
msgstr ""

#: templates/single-files-library.php:59
msgid "Welcome back! You can download this file directly."
msgstr ""

#: templates/single-files-library.php:63 templates/single-files-library.php:113
msgid "Download File"
msgstr ""

#: templates/single-files-library.php:71
msgid "To download this file, please provide your contact information below:"
msgstr ""

#: templates/single-files-library.php:75
#. translators: 1: opening link tag, 2: closing link tag
msgid "Already have an account? %1$sLogin here%2$s for direct downloads."
msgstr ""

#: templates/single-files-library.php:89
msgid "Full Name *"
msgstr ""

#: templates/single-files-library.php:94
msgid "Email Address (Optional)"
msgstr ""

#: templates/single-files-library.php:101
msgid "Mobile Number *"
msgstr ""

#: templates/single-files-library.php:124
msgid "Thank you!"
msgstr ""

#: templates/single-files-library.php:125
msgid ""
"Your download will start automatically. If it doesn't start, click the "
"button below."
msgstr ""

#: templates/single-files-library.php:127
msgid "Download Now"
msgstr ""

#: templates/single-files-library.php:134
msgid "No file available for download."
msgstr ""

#: templates/single-files-library.php:143
msgid "File Information"
msgstr ""

#: templates/single-files-library.php:160
msgid "Downloads:"
msgstr ""

#: templates/single-files-library.php:170
msgid "Last Updated:"
msgstr ""

#: templates/single-files-library.php:179
msgid "Categories"
msgstr ""

#: templates/single-files-library.php:218
msgid "Related Files"
msgstr ""

#: templates/single-files-library.php:252
msgid "← Back to Files Library"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "The Library"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://github.com/Abdalsalaam/the-library"
msgstr ""

#. Description of the plugin/theme
msgid ""
"A comprehensive files/books/videos library plugin with user data collection "
"for downloads."
msgstr ""

#. Author of the plugin/theme
msgid "Abdalsalaam Halawa"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://github.com/Abdalsalaam"
msgstr ""

#: includes/class-custom-post-type.php:38
msgctxt "Post type general name"
msgid "Files Library"
msgstr ""

#: includes/class-custom-post-type.php:39
msgctxt "Post type singular name"
msgid "File"
msgstr ""

#: includes/class-custom-post-type.php:40
msgctxt "Admin Menu text"
msgid "Files Library"
msgstr ""

#: includes/class-custom-post-type.php:41
msgctxt "Add New on Toolbar"
msgid "File"
msgstr ""

#: includes/class-custom-post-type.php:52
msgctxt "Overrides the \"Featured Image\" phrase"
msgid "File Featured Image"
msgstr ""

#: includes/class-custom-post-type.php:53
msgctxt "Overrides the \"Set featured image\" phrase"
msgid "Set featured image"
msgstr ""

#: includes/class-custom-post-type.php:54
msgctxt "Overrides the \"Remove featured image\" phrase"
msgid "Remove featured image"
msgstr ""

#: includes/class-custom-post-type.php:55
msgctxt "Overrides the \"Use as featured image\" phrase"
msgid "Use as featured image"
msgstr ""

#: includes/class-custom-post-type.php:56
msgctxt "The post type archive label"
msgid "File archives"
msgstr ""

#: includes/class-custom-post-type.php:57
msgctxt "Overrides the \"Insert into post\" phrase"
msgid "Insert into file"
msgstr ""

#: includes/class-custom-post-type.php:58
msgctxt "Overrides the \"Uploaded to this post\" phrase"
msgid "Uploaded to this file"
msgstr ""

#: includes/class-custom-post-type.php:59
msgctxt "Screen reader text for the filter links"
msgid "Filter files list"
msgstr ""

#: includes/class-custom-post-type.php:60
msgctxt "Screen reader text for the pagination"
msgid "Files list navigation"
msgstr ""

#: includes/class-custom-post-type.php:61
msgctxt "Screen reader text for the items list"
msgid "Files list"
msgstr ""

#: includes/class-custom-post-type.php:90
msgctxt "taxonomy general name"
msgid "File Categories"
msgstr ""

#: includes/class-custom-post-type.php:91
msgctxt "taxonomy singular name"
msgid "File Category"
msgstr ""