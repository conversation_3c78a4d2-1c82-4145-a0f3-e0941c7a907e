/**
 * Frontend Styles for The Library
 */

/* Archive Page Styles */
.wprl-archive-wrapper {
    padding: 2rem 0;
}

.wprl-archive-header {
    text-align: center;
    margin-bottom: 3rem;
}

.wprl-archive-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.wprl-archive-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Modern Filters */
.wprl-filters-wrapper {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.wprl-filters-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* All-in-one Layout */
.wprl-filters-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.wprl-filters-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

/* Search Field */
.wprl-search-field {
    position: relative;
    flex: 2;
    min-width: 250px;
}

.wprl-search-label {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    pointer-events: none;
}

.wprl-search-icon {
    color: #9ca3af;
    transition: color 0.2s ease;
}

.wprl-search-input {
    width: 100%;
    height: 48px;
    padding: 0 3rem 0 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 24px;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.2s ease;
    outline: none;
}

.wprl-search-input:focus {
    border-color: #f49865;
}

.wprl-search-input:focus + .wprl-search-clear,
.wprl-search-input:not(:placeholder-shown) ~ .wprl-search-label .wprl-search-icon {
    color: #f49865;
}

.wprl-search-clear {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    color: #9ca3af;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wprl-search-clear:hover {
    background: #f3f4f6;
    color: #ef4444;
}

.wprl-filter-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    min-width: 180px;
}

.wprl-filter-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.wprl-select-wrapper {
    position: relative;
}

.wprl-category-filter,
.wprl-file-type-filter,
.wprl-sort-filter {
    width: 100%;
    height: 44px;
    padding: 0 2.5rem 0 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.wprl-category-filter:focus,
.wprl-file-type-filter:focus,
.wprl-sort-filter:focus {
    border-color: #f49865;
}

.wprl-select-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #9ca3af;
    transition: color 0.2s ease;
}

.wprl-select-wrapper:hover .wprl-select-arrow {
    color: #f49865;
}

/* Action Buttons */
.wprl-filter-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.wprl-filter-submit {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #f49865 0%, #e67e22 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.wprl-filter-submit:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.wprl-filter-submit:active {
    transform: translateY(1px);
}

.wprl-clear-filters {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: #ffffff;
}

.wprl-clear-filters:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    text-decoration: none;
}

.wprl-button-icon {
    flex-shrink: 0;
}

/* Results Info */
.wprl-results-info {
    margin-bottom: 1.5rem;
    color: #666;
    font-size: 0.9rem;
}

/* Files Grid */
.wprl-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.wprl-file-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.wprl-file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.wprl-file-thumbnail {
    position: relative;
    overflow: hidden;
}

.wprl-file-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s;
}

.wprl-file-card:hover .wprl-file-thumbnail img {
    transform: scale(1.05);
}

.wprl-file-content {
    padding: 1.5rem;
}

.wprl-file-categories {
    margin-bottom: 1rem;
}

.wprl-category-tag {
    display: inline-block;
    margin: 1px;
    padding: 5px 12px;
    border: 1px solid var(--brdcolor-gray-300);
    text-transform: capitalize;
    font-size: 12px !important;
    border-radius: calc(var(--wd-brd-radius) / 1.5);
}

.wprl-category-tag:hover {
    background: #f49865;
    color: white;
    text-decoration: none;
}

.wprl-file-excerpt {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.wprl-file-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #888;
}

.wprl-file-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.wprl-file-actions {
    text-align: center;
}

/* Pagination */
.wprl-pagination {
    text-align: center;
    margin-top: 3rem;
}

.wprl-pagination .page-numbers {
    display: flex;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
    list-style: none;
}

.wprl-pagination .page-numbers li{
    margin-bottom: 0;
}

.wprl-pagination .page-numbers.current {
    background: #f49865;
    color: white;
    border-color: #f49865;
}

/* No Files */
.wprl-no-files {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.wprl-browse-all {
    background: #f49865;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    margin-top: 1rem;
    transition: background-color 0.3s;
}

.wprl-browse-all:hover {
    background: #f49865;
    color: white;
    text-decoration: none;
}

/* Single File Page */
.wprl-single-wrapper {
    padding: 2rem 0;
}

.wprl-separator {
    margin: 0 0.5rem;
    color: #666;
}

.wprl-current {
    color: #666;
}

.wprl-file-header {
    margin-bottom: 3rem;
}


.wprl-file-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.wprl-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.wprl-file-content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.wprl-file-featured-image {
    margin-bottom: 2rem;
}

.wprl-file-featured-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.wprl-file-description {
    line-height: 1.6;
    margin-bottom: 3rem;
}

/* Download Section */
.wprl-download-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
}

.wprl-download-section h3 {
    margin-top: 0;
    color: #333;
}

.wprl-download-info {
    margin-bottom: 2rem;
}

.wprl-download-form {
    max-width: 500px;
}

.wprl-form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.wprl-form-field {
    flex: 1;
}

.wprl-form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #333;
}

.wprl-form-field input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.wprl-form-field input:focus {
    outline: none;
    border-color: #f49865;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.wprl-form-field input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.wprl-form-field input.error:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.wprl-form-actions {
    margin-top: 2rem;
}

.wprl-download-button {
    background: #f49865;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 4px;
    font-size: 1.1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s;
}

.wprl-download-button:hover {
    background: #db885a;
    color: #ffffff;
}

.wprl-download-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.wprl-message {
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
}

.wprl-message.wprl-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.wprl-message.wprl-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.wprl-download-success {
    text-align: center;
    padding: 2rem;
}

.wprl-success-message h4 {
    color: #f49865;
    margin-bottom: 1rem;
}

.wprl-manual-download-button {
    background: #f49865;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 4px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s;
}

/* Sidebar */
.wprl-file-sidebar {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    height: fit-content;
}

.wprl-sidebar-widget {
    margin-bottom: 2rem;
}

.wprl-sidebar-widget:last-child {
    margin-bottom: 0;
}

.wprl-sidebar-widget h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #333;
    border-bottom: 2px solid #f49865;
    padding-bottom: 0.5rem;
}

.wprl-file-info-list,
.wprl-categories-list,
.wprl-related-files {
    list-style: none;
    padding: 0;
    margin: 0;
}

.wprl-file-info-list li,
.wprl-categories-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.wprl-file-info-list li:last-child,
.wprl-categories-list li:last-child {
    border-bottom: none;
}

.wprl-categories-list a {
    color: #f49865;
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wprl-categories-list a:hover {
    text-decoration: underline;
}

.wprl-category-count {
    color: #666;
    font-size: 0.9rem;
}

.wprl-related-files li {
    margin-bottom: 1rem;
}

.wprl-related-file {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s;
    padding: 0.5rem;
    border-radius: 4px;
}

.wprl-related-file:hover {
    background: white;
    text-decoration: none;
    color: #333;
}

.wprl-related-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.wprl-related-content h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.wprl-related-date {
    font-size: 0.8rem;
    color: #666;
}

.wprl-back-to-library {
    display: inline-block;
    color: #f49865;
    text-decoration: none;
    padding: 0.75rem 1rem;
    border: 1px solid #f49865;
    border-radius: 4px;
    transition: all 0.3s;
}

.wprl-back-to-library:hover,
.wprl-manual-download-button:hover{
    background: #f49865;
    color: white;
    text-decoration: none;
}

/* Responsive Design */

/* Large Desktop: All fields in one line */
@media (min-width: 1200px) {
    .wprl-filters-row {
        flex-wrap: nowrap;
    }

    .wprl-search-field {
        flex: 2;
        min-width: 300px;
    }

    .wprl-filter-field {
        flex: 1;
        min-width: 160px;
    }

    .wprl-filter-actions {
        flex-shrink: 0;
        min-width: auto;
    }
}

/* Medium Desktop/Tablet: 2-3 fields per line */
@media (min-width: 768px) and (max-width: 1199px) {
    .wprl-filters-row {
        flex-wrap: wrap;
    }

    .wprl-search-field {
        flex: 1 1 100%;
        min-width: 100%;
        margin-bottom: 1rem;
    }

    .wprl-filter-field {
        flex: 1 1 calc(33.333% - 0.67rem);
        min-width: 200px;
    }

    .wprl-filter-actions {
        flex: 1 1 100%;
        justify-content: center;
        margin-top: 1rem;
    }
}

/* Mobile: All fields stacked */
@media (max-width: 767px) {
    .wprl-filters-wrapper {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .wprl-filters-container {
        gap: 1.25rem;
    }

    .wprl-filters-row {
        flex-direction: column;
        gap: 1rem;
    }

    .wprl-search-field,
    .wprl-filter-field {
        flex: 1 1 100%;
        min-width: 100%;
    }

    .wprl-filter-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .wprl-filter-submit,
    .wprl-clear-filters {
        width: 100%;
        justify-content: center;
    }

    .wprl-files-grid {
        grid-template-columns: 1fr;
    }

    .wprl-file-content-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .wprl-form-row {
        flex-direction: column;
        gap: 0;
    }

    .wprl-file-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .wprl-archive-wrapper,
    .wprl-single-wrapper {
        padding: 1rem 0;
    }

    .wprl-filters-wrapper {
        padding: 1rem;
        border-radius: 8px;
    }

    .wprl-search-input {
        height: 44px;
        border-radius: 22px;
        font-size: 0.95rem;
    }

    .wprl-category-filter,
    .wprl-file-type-filter,
    .wprl-sort-filter {
        height: 40px;
        font-size: 0.9rem;
    }

    .wprl-filter-submit,
    .wprl-clear-filters {
        padding: 0.625rem 1.5rem;
        font-size: 0.95rem;
    }

    .wprl-download-section,
    .wprl-file-sidebar {
        padding: 1rem;
    }
}
