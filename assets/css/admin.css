/**
 * Admin Styles for The Library
 */

/* File Upload Meta Box */
.wprl-file-upload-wrapper {
    margin: 1rem 0;
}

.wprl-file-upload-preview {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.wprl-file-upload-preview.hidden {
    display: none;
}

.wprl-file-icon {
    width: 48px;
    height: 48px;
    background: #007cba;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.8rem;
}

.wprl-file-details h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.wprl-file-details p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.wprl-remove-file {
    margin-left: auto;
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.wprl-remove-file:hover {
    background: #c82333;
}

/* File Details Meta Box */
.wprl-file-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.wprl-stat-item {
    text-align: center;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.wprl-stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #007cba;
    margin-bottom: 0.25rem;
}

.wprl-stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Download Requests Page */
.wprl-download-requests-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.wprl-export-buttons {
    display: flex;
    gap: 0.5rem;
}

.wprl-export-button {
    background: #28a745;
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.wprl-export-button:hover {
    background: #218838;
    color: white;
    text-decoration: none;
}

.wprl-stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.wprl-stat-card {
    background: white;
    padding: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.wprl-stat-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    color: #007cba;
}

.wprl-stat-card p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Search Form */
.wprl-search-form {
    margin-bottom: 1rem;
}

.wprl-search-form .search-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.wprl-search-form input[type="search"] {
    width: 300px;
}

/* Bulk Actions */
.wprl-bulk-actions {
    margin-bottom: 1rem;
}

.wprl-bulk-actions select,
.wprl-bulk-actions input[type="submit"] {
    margin-right: 0.5rem;
}

/* Table Styles */
.wprl-requests-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.wprl-requests-table th,
.wprl-requests-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.wprl-requests-table th {
    background: #f9f9f9;
    font-weight: bold;
    color: #333;
}

.wprl-requests-table tr:hover {
    background: #f5f5f5;
}

.wprl-requests-table .column-checkbox {
    width: 40px;
    text-align: center;
}

.wprl-requests-table .column-file {
    width: 200px;
}

.wprl-requests-table .column-name {
    width: 150px;
}

.wprl-requests-table .column-email {
    width: 200px;
}

.wprl-requests-table .column-mobile {
    width: 120px;
}

.wprl-requests-table .column-date {
    width: 150px;
}

.wprl-requests-table .column-ip {
    width: 120px;
}

.wprl-requests-table .column-actions {
    width: 100px;
    text-align: center;
}

.wprl-delete-request {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
}

.wprl-delete-request:hover {
    background: #c82333;
}

/* No Items */
.wprl-no-items {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-style: italic;
}

/* Pagination */
.wprl-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.wprl-pagination-info {
    color: #666;
    font-size: 0.9rem;
}

.wprl-pagination-links {
    display: flex;
    gap: 0.25rem;
}

.wprl-pagination-links a,
.wprl-pagination-links span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #333;
    border-radius: 3px;
}

.wprl-pagination-links a:hover {
    background: #f0f0f0;
}

.wprl-pagination-links .current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

/* Filters */
.wprl-filters {
    background: #f9f9f9;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.wprl-filters-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
    flex-wrap: wrap;
}

.wprl-filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.wprl-filter-group label {
    font-size: 0.9rem;
    font-weight: bold;
    color: #333;
}

.wprl-filter-group input,
.wprl-filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.wprl-filter-actions {
    display: flex;
    gap: 0.5rem;
}

.wprl-filter-submit {
    background: #007cba;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 3px;
    cursor: pointer;
}

.wprl-filter-submit:hover {
    background: #005a87;
}

.wprl-filter-clear {
    background: #6c757d;
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 3px;
}

.wprl-filter-clear:hover {
    background: #545b62;
    color: white;
    text-decoration: none;
}

/* Loading States */
.wprl-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wprl-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: wprl-spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes wprl-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.wprl-notice {
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid;
    border-radius: 0 4px 4px 0;
}

.wprl-notice.success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.wprl-notice.error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.wprl-notice.warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.wprl-log-level {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.wprl-log-error {
    background: #dc3232;
    color: #fff;
}

.wprl-log-warning {
    background: #ffb900;
    color: #000;
}

.wprl-log-info {
    background: #00a0d2;
    color: #fff;
}

.wprl-log-debug {
    background: #666;
    color: #fff;
}

.wprl-notice.info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wprl-download-requests-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .wprl-stats-overview {
        grid-template-columns: 1fr;
    }

    .wprl-filters-row {
        flex-direction: column;
        align-items: stretch;
    }

    .wprl-requests-table {
        font-size: 0.9rem;
    }

    .wprl-requests-table th,
    .wprl-requests-table td {
        padding: 0.5rem;
    }

    .wprl-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .wprl-export-buttons {
        flex-direction: column;
    }

    .wprl-search-form input[type="search"] {
        width: 100%;
    }

    .wprl-requests-table {
        font-size: 0.8rem;
    }

    .wprl-requests-table .column-file,
    .wprl-requests-table .column-email {
        width: auto;
        min-width: 120px;
    }
}
